/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, Card, Checkbox, Table } from 'antd';
import React from 'react';
import { usePatchTable } from '../../_utils/usePatchTable';
import AuthorizedExtend from '@/components/Authorized/AuthorizedExtend';

const PatchTable = props => {
  const {
    loading,
    columns,
    tableTitle,
    selectRows = [],
    localData = [],
    paginatedData = [],
    pagination,
    setSelectRows,
    onUpload,
    onDelete,
    onCheckedAll,
    onTableChange,
  } = usePatchTable(props);

  return (
    <Card
      title={tableTitle}
      extra={
        <AuthorizedExtend auth="2" patchId>
          <div>
            <Button
              type="primary"
              disabled={!selectRows.length}
              onClick={onUpload}
              loading={loading}
            >
              上传
              {selectRows.length ? `(${selectRows.length})` : ''}
            </Button>
            <Button style={{ marginLeft: 10 }} disabled={!selectRows.length} onClick={onDelete}>
              删除
            </Button>
          </div>
        </AuthorizedExtend>
      }
      bodyStyle={{
        minHeight: '490px',
      }}
    >
      <div style={{ margin: '-10px 0 10px 20px' }}>
        {!!localData.length && (
          <Checkbox
            onChange={onCheckedAll}
            checked={selectRows.length && selectRows.length == localData.length}
            indeterminate={!!selectRows.length && selectRows.length != localData.length}
          >
            全选
          </Checkbox>
        )}
        {!!selectRows.length && (
          <span>
            已选择
            <a>{selectRows.length}</a>项
          </span>
        )}
      </div>

      <Table
        rowKey="waybill"
        dataSource={paginatedData}
        columns={columns}
        pagination={pagination}
        onChange={onTableChange}
        scroll={{ y: 500, x: 1400 }}
        rowSelection={{
          selectedRowKeys: selectRows,
          onChange: setSelectRows,
        }}
      />
    </Card>
  );
};

export default PatchTable;
